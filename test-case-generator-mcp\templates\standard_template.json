{"template_name": "标准测试用例模板", "version": "1.0", "description": "适用于大多数功能测试的标准模板", "fields": {"test_case_id": {"label": "测试用例ID", "type": "string", "required": true, "format": "TC_{category}_{sequence:04d}", "description": "唯一标识测试用例的ID"}, "title": {"label": "测试用例标题", "type": "string", "required": true, "max_length": 100, "description": "简洁明确的测试用例标题"}, "description": {"label": "测试描述", "type": "text", "required": true, "description": "详细描述测试的目的和范围"}, "category": {"label": "测试分类", "type": "enum", "required": true, "options": ["功能测试", "界面测试", "性能测试", "安全测试", "兼容性测试", "易用性测试"], "description": "测试用例的分类"}, "priority": {"label": "优先级", "type": "enum", "required": true, "options": ["high", "medium", "low"], "default": "medium", "description": "测试用例的执行优先级"}, "preconditions": {"label": "前置条件", "type": "array", "item_type": "string", "description": "执行测试前需要满足的条件"}, "test_steps": {"label": "测试步骤", "type": "array", "item_type": "string", "required": true, "min_items": 1, "description": "详细的测试执行步骤"}, "expected_results": {"label": "预期结果", "type": "array", "item_type": "string", "required": true, "description": "每个测试步骤的预期结果"}, "test_data": {"label": "测试数据", "type": "object", "description": "测试所需的数据"}, "environment": {"label": "测试环境", "type": "string", "default": "测试环境", "description": "执行测试的环境要求"}, "estimated_time": {"label": "预估执行时间", "type": "number", "unit": "minutes", "default": 5, "min": 1, "max": 120, "description": "预估的测试执行时间（分钟）"}, "tags": {"label": "标签", "type": "array", "item_type": "string", "description": "用于分类和搜索的标签"}, "related_requirements": {"label": "相关需求", "type": "string", "description": "关联的需求文档或需求ID"}, "risk_level": {"label": "风险等级", "type": "enum", "options": ["high", "medium", "low"], "default": "medium", "description": "测试失败的风险等级"}, "automation_potential": {"label": "自动化可能性", "type": "enum", "options": ["high", "medium", "low", "none"], "default": "medium", "description": "该测试用例自动化的可能性"}, "notes": {"label": "备注", "type": "text", "description": "额外的注意事项或说明"}, "created_by": {"label": "创建人", "type": "string", "default": "test_generator", "description": "测试用例创建者"}, "created_at": {"label": "创建时间", "type": "datetime", "auto_generate": true, "description": "测试用例创建时间"}, "updated_at": {"label": "更新时间", "type": "datetime", "auto_update": true, "description": "测试用例最后更新时间"}, "status": {"label": "状态", "type": "enum", "options": ["draft", "review", "approved", "deprecated"], "default": "draft", "description": "测试用例的状态"}}, "validation_rules": [{"rule": "test_steps_count_equals_expected_results_count", "description": "测试步骤数量应该与预期结果数量相等"}, {"rule": "title_not_empty", "description": "标题不能为空"}, {"rule": "at_least_one_test_step", "description": "至少需要一个测试步骤"}], "export_formats": {"excel": {"sheet_name": "测试用例", "column_mapping": {"test_case_id": "A", "title": "B", "category": "C", "priority": "D", "description": "E", "preconditions": "F", "test_steps": "G", "expected_results": "H", "estimated_time": "I", "status": "J"}}, "testlink": {"xml_mapping": {"name": "title", "summary": "description", "preconditions": "preconditions", "importance": "priority"}}}}