#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
包含MCP服务器的各种配置选项
"""

import os
from typing import Dict, Any, List
from pathlib import Path


class Config:
    """配置类"""
    
    # 服务器配置
    SERVER_HOST = os.getenv("MCP_HOST", "127.0.0.1")
    SERVER_PORT = int(os.getenv("MCP_PORT", "8002"))
    SERVER_TRANSPORT = os.getenv("MCP_TRANSPORT", "sse")
    
    # 项目路径
    PROJECT_ROOT = Path(__file__).parent
    TEMPLATES_DIR = PROJECT_ROOT / "templates"
    EXPORTS_DIR = PROJECT_ROOT / "exports"
    EXAMPLES_DIR = PROJECT_ROOT / "examples"
    LOGS_DIR = PROJECT_ROOT / "logs"
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    LOG_ROTATION = "10 MB"
    LOG_RETENTION = "7 days"
    
    # 测试用例生成配置
    DEFAULT_PRIORITY = "medium"
    DEFAULT_ESTIMATED_TIME = 5  # 分钟
    DEFAULT_CATEGORY = "功能测试"
    DEFAULT_STATUS = "draft"
    
    # 支持的优先级
    PRIORITY_LEVELS = ["high", "medium", "low"]
    
    # 支持的测试分类
    TEST_CATEGORIES = [
        "功能测试",
        "界面测试", 
        "性能测试",
        "安全测试",
        "兼容性测试",
        "易用性测试",
        "接口测试",
        "数据库测试",
        "业务流程测试",
        "边界值测试",
        "异常测试",
        "回归测试"
    ]
    
    # 支持的测试状态
    TEST_STATUSES = ["draft", "review", "approved", "deprecated", "blocked"]
    
    # UI元素类型
    UI_ELEMENT_TYPES = [
        "button",
        "input",
        "textarea", 
        "select",
        "checkbox",
        "radio",
        "link",
        "image",
        "table",
        "form",
        "menu",
        "dialog",
        "tab",
        "accordion"
    ]
    
    # 导出格式配置
    EXPORT_FORMATS = {
        "excel": {
            "extension": ".xlsx",
            "mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "max_rows": 1000000,
            "default_sheet_name": "测试用例"
        },
        "csv": {
            "extension": ".csv",
            "mime_type": "text/csv",
            "encoding": "utf-8-sig",
            "delimiter": ","
        },
        "json": {
            "extension": ".json",
            "mime_type": "application/json",
            "encoding": "utf-8",
            "indent": 2
        },
        "xml": {
            "extension": ".xml",
            "mime_type": "application/xml",
            "encoding": "utf-8"
        }
    }
    
    # TestLink集成配置
    TESTLINK_CONFIG = {
        "default_testsuite_name": "导入的测试套件",
        "default_importance": "2",  # 1=低, 2=中, 3=高
        "default_execution_type": "1",  # 1=手工, 2=自动
        "xml_version": "1.0",
        "xml_encoding": "UTF-8"
    }
    
    # Jira集成配置
    JIRA_CONFIG = {
        "issue_type": "Test",
        "default_priority": "Medium",
        "custom_fields": {
            "test_type": "customfield_testtype",
            "preconditions": "customfield_preconditions", 
            "test_steps": "customfield_teststeps"
        }
    }
    
    # 需求分析配置
    REQUIREMENTS_ANALYSIS = {
        "keywords": {
            "action_words": [
                "输入", "点击", "选择", "提交", "验证", "显示", "跳转", 
                "登录", "注册", "搜索", "筛选", "排序", "添加", "删除",
                "修改", "保存", "取消", "确认", "上传", "下载"
            ],
            "condition_words": [
                "如果", "当", "则", "否则", "满足", "不满足", "正确", "错误",
                "成功", "失败", "有效", "无效", "存在", "不存在"
            ],
            "validation_words": [
                "验证", "检查", "确认", "校验", "提示", "错误", "警告",
                "成功", "失败", "通过", "拒绝"
            ],
            "ui_elements": [
                "按钮", "输入框", "下拉框", "复选框", "单选框", "链接",
                "菜单", "页面", "弹窗", "表格", "列表", "图片"
            ]
        },
        "user_story_patterns": {
            "role": r"作为\s*([^，,]+)",
            "function": r"我想要\s*([^，,。.]+)",
            "value": r"以便\s*([^，,。.]+)"
        },
        "acceptance_criteria_patterns": {
            "given": r"(Given|假设|给定)\s*(.+)",
            "when": r"(When|当|如果)\s*(.+)", 
            "then": r"(Then|那么|则)\s*(.+)"
        }
    }
    
    # 测试数据生成配置
    TEST_DATA_CONFIG = {
        "default_user": {
            "username": "testuser",
            "password": "Test123456",
            "email": "<EMAIL>"
        },
        "boundary_values": {
            "string_min_length": 1,
            "string_max_length": 255,
            "number_min": -2147483648,
            "number_max": 2147483647,
            "age_min": 0,
            "age_max": 150
        },
        "common_invalid_inputs": [
            "",  # 空值
            " ",  # 空格
            "null",
            "undefined",
            "<script>alert('xss')</script>",  # XSS测试
            "'; DROP TABLE users; --",  # SQL注入测试
            "很长很长的字符串" * 100  # 超长字符串
        ]
    }
    
    # 性能配置
    PERFORMANCE_CONFIG = {
        "max_test_cases_per_request": 100,
        "max_export_file_size": 50 * 1024 * 1024,  # 50MB
        "request_timeout": 30,  # 秒
        "max_concurrent_requests": 10
    }
    
    @classmethod
    def ensure_directories(cls):
        """确保必要的目录存在"""
        directories = [
            cls.TEMPLATES_DIR,
            cls.EXPORTS_DIR,
            cls.LOGS_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_template_path(cls, template_name: str) -> Path:
        """获取模板文件路径"""
        return cls.TEMPLATES_DIR / f"{template_name}.json"
    
    @classmethod
    def get_export_path(cls, filename: str) -> Path:
        """获取导出文件路径"""
        return cls.EXPORTS_DIR / filename
    
    @classmethod
    def validate_priority(cls, priority: str) -> bool:
        """验证优先级是否有效"""
        return priority.lower() in cls.PRIORITY_LEVELS
    
    @classmethod
    def validate_category(cls, category: str) -> bool:
        """验证测试分类是否有效"""
        return category in cls.TEST_CATEGORIES
    
    @classmethod
    def get_log_config(cls) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            "handlers": [
                {
                    "sink": cls.LOGS_DIR / "mcp_server.log",
                    "format": cls.LOG_FORMAT,
                    "level": cls.LOG_LEVEL,
                    "rotation": cls.LOG_ROTATION,
                    "retention": cls.LOG_RETENTION,
                    "compression": "zip"
                },
                {
                    "sink": "sys.stdout",
                    "format": cls.LOG_FORMAT,
                    "level": cls.LOG_LEVEL
                }
            ]
        }


# 创建全局配置实例
config = Config()

# 确保目录存在
config.ensure_directories()
