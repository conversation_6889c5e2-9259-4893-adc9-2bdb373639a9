#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用例生成器 MCP 客户端
用于测试和演示MCP服务器功能
"""

import asyncio
import json
from typing import Dict, Any
from fastmcp import Client
from loguru import logger


class TestCaseGeneratorClient:
    """测试用例生成器客户端"""
    
    def __init__(self, server_url: str = "http://127.0.0.1:8002/sse"):
        self.server_url = server_url
        self.client = None
    
    async def connect(self):
        """连接到MCP服务器"""
        try:
            self.client = Client(self.server_url)
            await self.client.__aenter__()
            logger.info(f"成功连接到MCP服务器: {self.server_url}")
            return True
        except Exception as e:
            logger.error(f"连接MCP服务器失败: {str(e)}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.client:
            await self.client.__aexit__(None, None, None)
            logger.info("已断开MCP服务器连接")
    
    async def list_tools(self):
        """列出可用工具"""
        try:
            tools = await self.client.list_tools()
            logger.info(f"可用工具数量: {len(tools)}")
            for tool in tools:
                print(f"- {tool.name}: {tool.description}")
            return tools
        except Exception as e:
            logger.error(f"获取工具列表失败: {str(e)}")
            return []
    
    async def analyze_requirements_demo(self):
        """需求分析演示"""
        print("\n=== 需求分析演示 ===")
        
        requirements_text = """
        用户登录功能：
        作为系统用户，我想要通过用户名和密码登录系统，以便访问个人账户信息。
        
        验收标准：
        1. 用户输入正确的用户名和密码，系统验证成功后跳转到主页面
        2. 用户输入错误的用户名或密码，系统显示错误提示信息
        3. 用户名和密码输入框都是必填项
        4. 密码输入框应该隐藏输入内容
        """
        
        try:
            result = await self.client.call_tool(
                "analyze_requirements",
                {
                    "requirements_text": requirements_text,
                    "document_type": "user_story"
                }
            )
            
            print("需求分析结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
            
        except Exception as e:
            logger.error(f"需求分析失败: {str(e)}")
            return None
    
    async def generate_functional_tests_demo(self):
        """功能测试用例生成演示"""
        print("\n=== 功能测试用例生成演示 ===")
        
        try:
            result = await self.client.call_tool(
                "generate_functional_tests",
                {
                    "feature_name": "用户登录",
                    "feature_description": "用户通过用户名和密码登录系统",
                    "acceptance_criteria": [
                        "正确用户名密码可以登录",
                        "错误密码显示错误信息",
                        "用户名密码为必填项"
                    ],
                    "priority": "high"
                }
            )
            
            print("功能测试用例:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
            
        except Exception as e:
            logger.error(f"功能测试用例生成失败: {str(e)}")
            return None
    
    async def generate_ui_tests_demo(self):
        """UI测试用例生成演示"""
        print("\n=== UI测试用例生成演示 ===")
        
        ui_elements = [
            {"type": "input", "name": "用户名输入框", "id": "username"},
            {"type": "input", "name": "密码输入框", "id": "password"},
            {"type": "button", "name": "登录按钮", "id": "login-btn"},
            {"type": "link", "name": "忘记密码链接", "id": "forgot-password"}
        ]
        
        user_interactions = [
            "用户登录流程",
            "密码重置流程",
            "表单验证"
        ]
        
        try:
            result = await self.client.call_tool(
                "generate_ui_tests",
                {
                    "page_name": "登录页面",
                    "ui_elements": ui_elements,
                    "user_interactions": user_interactions
                }
            )
            
            print("UI测试用例:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
            
        except Exception as e:
            logger.error(f"UI测试用例生成失败: {str(e)}")
            return None
    
    async def export_to_excel_demo(self, test_cases):
        """Excel导出演示"""
        print("\n=== Excel导出演示 ===")
        
        if not test_cases:
            print("没有测试用例可导出")
            return
        
        try:
            result = await self.client.call_tool(
                "export_to_excel",
                {
                    "test_cases": test_cases,
                    "filename": "demo_test_cases.xlsx",
                    "include_summary": True
                }
            )
            
            print("Excel导出结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
            
        except Exception as e:
            logger.error(f"Excel导出失败: {str(e)}")
            return None
    
    async def run_demo(self):
        """运行完整演示"""
        print("🚀 手工测试用例生成器 MCP 演示")
        print("=" * 50)
        
        # 连接服务器
        if not await self.connect():
            return
        
        try:
            # 列出可用工具
            await self.list_tools()
            
            # 需求分析演示
            requirements_result = await self.analyze_requirements_demo()
            
            # 功能测试用例生成演示
            functional_tests = await self.generate_functional_tests_demo()
            
            # UI测试用例生成演示
            ui_tests = await self.generate_ui_tests_demo()
            
            # 收集所有测试用例用于导出
            all_test_cases = []
            if functional_tests and functional_tests.get("test_cases"):
                all_test_cases.extend(functional_tests["test_cases"])
            if ui_tests and ui_tests.get("test_cases"):
                all_test_cases.extend(ui_tests["test_cases"])
            
            # Excel导出演示
            if all_test_cases:
                await self.export_to_excel_demo(all_test_cases)
            
            print("\n✅ 演示完成！")
            
        finally:
            await self.disconnect()
    
    async def interactive_mode(self):
        """交互模式"""
        print("🎯 进入交互模式")
        print("可用命令:")
        print("1. analyze - 分析需求")
        print("2. functional - 生成功能测试用例")
        print("3. ui - 生成UI测试用例")
        print("4. boundary - 生成边界值测试用例")
        print("5. export - 导出测试用例")
        print("6. tools - 列出所有工具")
        print("7. quit - 退出")
        
        if not await self.connect():
            return
        
        try:
            while True:
                command = input("\n请输入命令: ").strip().lower()
                
                if command == "quit":
                    break
                elif command == "tools":
                    await self.list_tools()
                elif command == "analyze":
                    await self._interactive_analyze()
                elif command == "functional":
                    await self._interactive_functional()
                elif command == "ui":
                    await self._interactive_ui()
                elif command == "boundary":
                    await self._interactive_boundary()
                elif command == "export":
                    await self._interactive_export()
                else:
                    print("未知命令，请重新输入")
        
        finally:
            await self.disconnect()
    
    async def _interactive_analyze(self):
        """交互式需求分析"""
        print("\n请输入需求文档内容:")
        requirements = input().strip()
        
        if not requirements:
            print("需求内容不能为空")
            return
        
        try:
            result = await self.client.call_tool(
                "analyze_requirements",
                {
                    "requirements_text": requirements,
                    "document_type": "text"
                }
            )
            print("\n分析结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        except Exception as e:
            print(f"分析失败: {str(e)}")
    
    async def _interactive_functional(self):
        """交互式功能测试用例生成"""
        feature_name = input("请输入功能名称: ").strip()
        feature_description = input("请输入功能描述: ").strip()
        criteria_input = input("请输入验收标准(用分号分隔): ").strip()
        
        if not all([feature_name, feature_description, criteria_input]):
            print("所有字段都不能为空")
            return
        
        acceptance_criteria = [c.strip() for c in criteria_input.split(";")]
        
        try:
            result = await self.client.call_tool(
                "generate_functional_tests",
                {
                    "feature_name": feature_name,
                    "feature_description": feature_description,
                    "acceptance_criteria": acceptance_criteria,
                    "priority": "medium"
                }
            )
            print("\n生成的测试用例:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        except Exception as e:
            print(f"生成失败: {str(e)}")
    
    async def _interactive_ui(self):
        """交互式UI测试用例生成"""
        page_name = input("请输入页面名称: ").strip()
        
        if not page_name:
            print("页面名称不能为空")
            return
        
        # 简化的UI元素输入
        ui_elements = [
            {"type": "button", "name": "主要按钮", "id": "main-btn"},
            {"type": "input", "name": "输入框", "id": "input-field"}
        ]
        
        user_interactions = ["基本交互流程"]
        
        try:
            result = await self.client.call_tool(
                "generate_ui_tests",
                {
                    "page_name": page_name,
                    "ui_elements": ui_elements,
                    "user_interactions": user_interactions
                }
            )
            print("\n生成的UI测试用例:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        except Exception as e:
            print(f"生成失败: {str(e)}")
    
    async def _interactive_boundary(self):
        """交互式边界值测试用例生成"""
        print("边界值测试用例生成功能开发中...")
    
    async def _interactive_export(self):
        """交互式导出"""
        print("导出功能需要先生成测试用例")


async def main():
    """主函数"""
    client = TestCaseGeneratorClient()
    
    print("选择运行模式:")
    print("1. 演示模式 (demo)")
    print("2. 交互模式 (interactive)")
    
    mode = input("请选择模式 (1 或 2): ").strip()
    
    if mode == "1":
        await client.run_demo()
    elif mode == "2":
        await client.interactive_mode()
    else:
        print("无效选择")


if __name__ == "__main__":
    asyncio.run(main())
