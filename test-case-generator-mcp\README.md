# 手工测试用例生成器 MCP

一个基于 Model Context Protocol (MCP) 的智能手工测试用例生成工具，帮助测试人员快速生成高质量的手工测试用例。

## 功能特性

### 🔍 需求分析工具
- 分析需求文档，自动提取测试点
- 解析用户故事和验收标准
- 识别业务规则和约束条件
- 生成测试覆盖矩阵

### 📝 测试用例生成
- **功能测试用例**: 基于功能需求生成测试场景
- **UI测试用例**: 针对用户界面的交互测试
- **业务流程测试**: 端到端业务流程验证
- **边界值测试**: 输入边界和异常情况测试
- **兼容性测试**: 跨平台、跨浏览器测试用例

### 📊 测试管理
- 测试用例分类和优先级设置
- 测试执行时间估算
- 测试覆盖率分析
- 测试用例去重和优化

### 📤 多格式导出
- Excel 格式导出
- TestLink 兼容格式
- Jira 测试用例格式
- 自定义模板导出

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动服务器
```bash
python server.py
```

### 连接客户端
```bash
python client.py
```

## 工具列表

| 工具名称 | 功能描述 | 输入参数 |
|---------|---------|---------|
| `analyze_requirements` | 分析需求文档提取测试点 | 需求文档内容 |
| `generate_functional_tests` | 生成功能测试用例 | 功能描述、输入输出规范 |
| `generate_ui_tests` | 生成UI测试用例 | 界面描述、交互元素 |
| `create_test_scenarios` | 创建测试场景 | 业务流程描述 |
| `format_test_case` | 格式化测试用例 | 原始测试用例、目标格式 |
| `export_to_excel` | 导出Excel格式 | 测试用例列表 |

## 使用示例

### 1. 分析需求文档
```python
# 通过MCP调用
{
    "tool": "analyze_requirements",
    "arguments": {
        "requirements_text": "用户登录功能：用户输入用户名和密码，系统验证后跳转到主页面"
    }
}
```

### 2. 生成功能测试用例
```python
{
    "tool": "generate_functional_tests",
    "arguments": {
        "feature_name": "用户登录",
        "feature_description": "用户通过用户名密码登录系统",
        "acceptance_criteria": ["正确用户名密码可以登录", "错误密码显示错误信息"]
    }
}
```

## 项目结构
```
test-case-generator-mcp/
├── server.py              # MCP服务器主文件
├── client.py              # 测试客户端
├── tools/                 # 工具实现目录
│   ├── __init__.py
│   ├── requirements_analyzer.py    # 需求分析工具
│   ├── test_generator.py          # 测试用例生成工具
│   ├── formatter.py               # 格式化工具
│   └── exporter.py               # 导出工具
├── templates/             # 测试用例模板
├── examples/              # 使用示例
├── tests/                 # 单元测试
├── requirements.txt       # 依赖文件
└── README.md             # 项目说明
```

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

MIT License
