#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本使用示例
演示如何使用测试用例生成器MCP的各种功能
"""

import asyncio
import json
from fastmcp import Client


async def basic_requirements_analysis():
    """基本需求分析示例"""
    print("=== 需求分析示例 ===")
    
    async with Client("http://127.0.0.1:8002/sse") as client:
        # 用户故事格式的需求
        user_story = """
        作为电商网站的用户，我想要能够搜索商品，以便快速找到我需要的产品。
        
        验收标准：
        - 用户可以在搜索框中输入关键词
        - 系统返回相关的商品列表
        - 搜索结果按相关性排序
        - 支持按价格、品牌等条件筛选
        """
        
        result = await client.call_tool(
            "analyze_requirements",
            {
                "requirements_text": user_story,
                "document_type": "user_story"
            }
        )
        
        print("需求分析结果:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        return result


async def generate_login_tests():
    """生成登录功能测试用例示例"""
    print("\n=== 登录功能测试用例生成 ===")
    
    async with Client("http://127.0.0.1:8002/sse") as client:
        result = await client.call_tool(
            "generate_functional_tests",
            {
                "feature_name": "用户登录",
                "feature_description": "用户使用用户名和密码登录系统",
                "acceptance_criteria": [
                    "输入正确的用户名和密码可以成功登录",
                    "输入错误的用户名或密码显示错误提示",
                    "用户名和密码都是必填字段",
                    "登录成功后跳转到用户主页"
                ],
                "priority": "high"
            }
        )
        
        print("生成的功能测试用例:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        return result


async def generate_shopping_cart_ui_tests():
    """生成购物车UI测试用例示例"""
    print("\n=== 购物车UI测试用例生成 ===")
    
    async with Client("http://127.0.0.1:8002/sse") as client:
        ui_elements = [
            {"type": "button", "name": "添加到购物车按钮", "id": "add-to-cart"},
            {"type": "button", "name": "删除商品按钮", "id": "remove-item"},
            {"type": "input", "name": "商品数量输入框", "id": "quantity"},
            {"type": "button", "name": "结算按钮", "id": "checkout"},
            {"type": "link", "name": "继续购物链接", "id": "continue-shopping"}
        ]
        
        user_interactions = [
            "添加商品到购物车",
            "修改商品数量",
            "删除购物车商品",
            "清空购物车",
            "进行结算"
        ]
        
        result = await client.call_tool(
            "generate_ui_tests",
            {
                "page_name": "购物车页面",
                "ui_elements": ui_elements,
                "user_interactions": user_interactions
            }
        )
        
        print("生成的UI测试用例:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        return result


async def generate_form_boundary_tests():
    """生成表单边界值测试用例示例"""
    print("\n=== 表单边界值测试用例生成 ===")
    
    async with Client("http://127.0.0.1:8002/sse") as client:
        input_fields = [
            {
                "name": "用户年龄",
                "type": "number",
                "min": 18,
                "max": 120
            },
            {
                "name": "用户名",
                "type": "text",
                "min_length": 3,
                "max_length": 20
            },
            {
                "name": "密码",
                "type": "password",
                "min_length": 8,
                "max_length": 32
            }
        ]
        
        business_rules = [
            "用户年龄必须在18-120岁之间",
            "用户名长度必须在3-20个字符之间",
            "密码必须包含字母和数字",
            "密码长度必须在8-32个字符之间"
        ]
        
        result = await client.call_tool(
            "generate_boundary_tests",
            {
                "input_fields": input_fields,
                "business_rules": business_rules
            }
        )
        
        print("生成的边界值测试用例:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        return result


async def create_ecommerce_scenarios():
    """创建电商业务场景测试"""
    print("\n=== 电商业务场景测试创建 ===")
    
    async with Client("http://127.0.0.1:8002/sse") as client:
        workflow_description = """
        电商购物完整流程：
        1. 用户浏览商品
        2. 搜索和筛选商品
        3. 查看商品详情
        4. 添加商品到购物车
        5. 修改购物车内容
        6. 填写收货信息
        7. 选择支付方式
        8. 完成支付
        9. 查看订单状态
        """
        
        user_roles = [
            "普通用户",
            "VIP用户",
            "企业用户"
        ]
        
        business_processes = [
            "商品浏览购买",
            "订单管理",
            "退换货处理",
            "会员积分使用"
        ]
        
        result = await client.call_tool(
            "create_test_scenarios",
            {
                "workflow_description": workflow_description,
                "user_roles": user_roles,
                "business_processes": business_processes
            }
        )
        
        print("创建的测试场景:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        return result


async def format_and_export_tests():
    """格式化和导出测试用例示例"""
    print("\n=== 测试用例格式化和导出 ===")
    
    async with Client("http://127.0.0.1:8002/sse") as client:
        # 先生成一些测试用例
        login_tests = await client.call_tool(
            "generate_functional_tests",
            {
                "feature_name": "用户注册",
                "feature_description": "新用户注册账户",
                "acceptance_criteria": [
                    "填写完整注册信息可以成功注册",
                    "邮箱格式验证",
                    "密码强度验证"
                ],
                "priority": "high"
            }
        )
        
        if login_tests.get("test_cases"):
            test_cases = login_tests["test_cases"]
            
            # 格式化为详细格式
            for i, test_case in enumerate(test_cases[:1]):  # 只格式化第一个用例作为示例
                formatted = await client.call_tool(
                    "format_test_case",
                    {
                        "test_case_data": test_case,
                        "format_type": "detailed"
                    }
                )
                
                print(f"格式化的测试用例 {i+1}:")
                print(json.dumps(formatted, ensure_ascii=False, indent=2))
            
            # 导出到Excel
            export_result = await client.call_tool(
                "export_to_excel",
                {
                    "test_cases": test_cases,
                    "filename": "example_test_cases.xlsx",
                    "include_summary": True
                }
            )
            
            print("\nExcel导出结果:")
            print(json.dumps(export_result, ensure_ascii=False, indent=2))


async def validate_test_coverage():
    """验证测试覆盖率示例"""
    print("\n=== 测试覆盖率验证 ===")
    
    async with Client("http://127.0.0.1:8002/sse") as client:
        # 模拟一些测试用例
        test_cases = [
            {
                "title": "用户登录正向测试",
                "description": "验证用户使用正确用户名密码登录",
                "category": "功能测试"
            },
            {
                "title": "用户登录异常测试", 
                "description": "验证用户使用错误密码登录",
                "category": "异常测试"
            }
        ]
        
        requirements = [
            "用户可以使用用户名密码登录",
            "错误密码时显示错误信息",
            "用户可以重置密码",
            "用户可以记住登录状态"
        ]
        
        coverage_result = await client.call_tool(
            "validate_test_coverage",
            {
                "test_cases": test_cases,
                "requirements": requirements
            }
        )
        
        print("测试覆盖率验证结果:")
        print(json.dumps(coverage_result, ensure_ascii=False, indent=2))


async def main():
    """运行所有示例"""
    print("🚀 测试用例生成器MCP - 基本使用示例")
    print("=" * 60)
    
    try:
        # 需求分析
        await basic_requirements_analysis()
        
        # 功能测试用例生成
        await generate_login_tests()
        
        # UI测试用例生成
        await generate_shopping_cart_ui_tests()
        
        # 边界值测试用例生成
        await generate_form_boundary_tests()
        
        # 业务场景测试创建
        await create_ecommerce_scenarios()
        
        # 格式化和导出
        await format_and_export_tests()
        
        # 测试覆盖率验证
        await validate_test_coverage()
        
        print("\n✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"❌ 运行示例时出错: {str(e)}")
        print("请确保MCP服务器正在运行 (python server.py)")


if __name__ == "__main__":
    asyncio.run(main())
