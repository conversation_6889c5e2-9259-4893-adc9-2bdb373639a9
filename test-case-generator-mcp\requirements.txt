# MCP 核心依赖
mcp>=1.0.0
fastmcp>=0.9.0

# 数据处理
pandas>=2.0.0
openpyxl>=3.1.0
xlsxwriter>=3.1.0

# 文本处理和NLP
jieba>=0.42.1
nltk>=3.8.1
textblob>=0.17.1

# 模板引擎
jinja2>=3.1.0

# 数据验证
pydantic>=2.0.0

# 日期时间处理
python-dateutil>=2.8.0

# JSON处理
jsonschema>=4.17.0

# 文件处理
python-docx>=0.8.11
PyPDF2>=3.0.0

# HTTP客户端（用于API集成）
httpx>=0.24.0
requests>=2.31.0

# 配置管理
python-dotenv>=1.0.0

# 日志
loguru>=0.7.0

# 测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0

# 代码质量
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
