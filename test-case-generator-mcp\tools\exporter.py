#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用例导出工具
支持导出为Excel、CSV、JSON等多种格式
"""

import json
import os
from typing import Any, Dict, List, Optional
from datetime import datetime
import pandas as pd
from loguru import logger


class TestCaseExporter:
    """测试用例导出器"""
    
    def __init__(self, output_dir: str = "exports"):
        self.output_dir = output_dir
        self._ensure_output_dir()
    
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            logger.info(f"创建输出目录: {self.output_dir}")
    
    def export_to_excel(
        self,
        test_cases: List[Dict[str, Any]],
        filename: str = "test_cases.xlsx",
        include_summary: bool = True
    ) -> Dict[str, Any]:
        """
        导出测试用例到Excel文件
        
        参数:
        test_cases: 测试用例列表
        filename: 文件名
        include_summary: 是否包含汇总信息
        
        返回:
        导出结果信息
        """
        try:
            logger.info(f"开始导出Excel文件: {filename}")
            
            if not test_cases:
                return {"error": "没有测试用例可导出", "success": False}
            
            filepath = os.path.join(self.output_dir, filename)
            
            # 创建Excel写入器
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 主测试用例表
                df_main = self._prepare_main_dataframe(test_cases)
                df_main.to_excel(writer, sheet_name='测试用例', index=False)
                
                # 格式化主表
                self._format_main_sheet(writer.sheets['测试用例'], df_main)
                
                if include_summary:
                    # 汇总信息表
                    df_summary = self._prepare_summary_dataframe(test_cases)
                    df_summary.to_excel(writer, sheet_name='汇总信息', index=False)
                    
                    # 优先级统计表
                    df_priority = self._prepare_priority_dataframe(test_cases)
                    df_priority.to_excel(writer, sheet_name='优先级统计', index=False)
                    
                    # 分类统计表
                    df_category = self._prepare_category_dataframe(test_cases)
                    df_category.to_excel(writer, sheet_name='分类统计', index=False)
            
            file_size = os.path.getsize(filepath)
            logger.info(f"Excel文件导出成功: {filepath} ({file_size} bytes)")
            
            return {
                "success": True,
                "filepath": filepath,
                "filename": filename,
                "total_cases": len(test_cases),
                "file_size": file_size,
                "created_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Excel导出失败: {str(e)}")
            return {"error": str(e), "success": False}
    
    def export_to_csv(
        self,
        test_cases: List[Dict[str, Any]],
        filename: str = "test_cases.csv"
    ) -> Dict[str, Any]:
        """导出测试用例到CSV文件"""
        try:
            logger.info(f"开始导出CSV文件: {filename}")
            
            if not test_cases:
                return {"error": "没有测试用例可导出", "success": False}
            
            filepath = os.path.join(self.output_dir, filename)
            df = self._prepare_main_dataframe(test_cases)
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            file_size = os.path.getsize(filepath)
            logger.info(f"CSV文件导出成功: {filepath}")
            
            return {
                "success": True,
                "filepath": filepath,
                "filename": filename,
                "total_cases": len(test_cases),
                "file_size": file_size,
                "created_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"CSV导出失败: {str(e)}")
            return {"error": str(e), "success": False}
    
    def export_to_json(
        self,
        test_cases: List[Dict[str, Any]],
        filename: str = "test_cases.json",
        pretty_print: bool = True
    ) -> Dict[str, Any]:
        """导出测试用例到JSON文件"""
        try:
            logger.info(f"开始导出JSON文件: {filename}")
            
            if not test_cases:
                return {"error": "没有测试用例可导出", "success": False}
            
            filepath = os.path.join(self.output_dir, filename)
            
            export_data = {
                "metadata": {
                    "total_cases": len(test_cases),
                    "exported_at": datetime.now().isoformat(),
                    "version": "1.0"
                },
                "test_cases": test_cases
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                if pretty_print:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
                else:
                    json.dump(export_data, f, ensure_ascii=False)
            
            file_size = os.path.getsize(filepath)
            logger.info(f"JSON文件导出成功: {filepath}")
            
            return {
                "success": True,
                "filepath": filepath,
                "filename": filename,
                "total_cases": len(test_cases),
                "file_size": file_size,
                "created_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"JSON导出失败: {str(e)}")
            return {"error": str(e), "success": False}
    
    def export_to_testlink_xml(
        self,
        test_cases: List[Dict[str, Any]],
        filename: str = "testlink_import.xml",
        testsuite_name: str = "导入的测试套件"
    ) -> Dict[str, Any]:
        """导出为TestLink XML格式"""
        try:
            logger.info(f"开始导出TestLink XML文件: {filename}")
            
            if not test_cases:
                return {"error": "没有测试用例可导出", "success": False}
            
            filepath = os.path.join(self.output_dir, filename)
            xml_content = self._generate_testlink_xml(test_cases, testsuite_name)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(xml_content)
            
            file_size = os.path.getsize(filepath)
            logger.info(f"TestLink XML文件导出成功: {filepath}")
            
            return {
                "success": True,
                "filepath": filepath,
                "filename": filename,
                "total_cases": len(test_cases),
                "file_size": file_size,
                "created_at": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"TestLink XML导出失败: {str(e)}")
            return {"error": str(e), "success": False}
    
    def _prepare_main_dataframe(self, test_cases: List[Dict[str, Any]]) -> pd.DataFrame:
        """准备主要的测试用例数据框"""
        data = []
        
        for case in test_cases:
            row = {
                "测试用例ID": case.get("id", ""),
                "标题": case.get("title", ""),
                "描述": case.get("description", ""),
                "分类": case.get("category", ""),
                "优先级": case.get("priority", "medium"),
                "前置条件": self._format_list_for_excel(case.get("preconditions", [])),
                "测试步骤": self._format_list_for_excel(case.get("test_steps", [])),
                "预期结果": self._format_list_for_excel(case.get("expected_results", [])),
                "标签": ", ".join(case.get("tags", [])),
                "预估时间(分钟)": case.get("estimated_time", 5),
                "状态": case.get("status", "draft"),
                "创建时间": case.get("created_at", "")
            }
            data.append(row)
        
        return pd.DataFrame(data)
    
    def _prepare_summary_dataframe(self, test_cases: List[Dict[str, Any]]) -> pd.DataFrame:
        """准备汇总信息数据框"""
        total_cases = len(test_cases)
        total_time = sum([case.get("estimated_time", 5) for case in test_cases])
        
        categories = {}
        priorities = {}
        statuses = {}
        
        for case in test_cases:
            # 统计分类
            category = case.get("category", "未分类")
            categories[category] = categories.get(category, 0) + 1
            
            # 统计优先级
            priority = case.get("priority", "medium")
            priorities[priority] = priorities.get(priority, 0) + 1
            
            # 统计状态
            status = case.get("status", "draft")
            statuses[status] = statuses.get(status, 0) + 1
        
        summary_data = [
            {"项目": "测试用例总数", "数值": total_cases},
            {"项目": "预估总时间(分钟)", "数值": total_time},
            {"项目": "预估总时间(小时)", "数值": round(total_time / 60, 2)},
            {"项目": "高优先级用例数", "数值": priorities.get("high", 0)},
            {"项目": "中优先级用例数", "数值": priorities.get("medium", 0)},
            {"项目": "低优先级用例数", "数值": priorities.get("low", 0)},
            {"项目": "不同分类数量", "数值": len(categories)},
            {"项目": "导出时间", "数值": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
        ]
        
        return pd.DataFrame(summary_data)
    
    def _prepare_priority_dataframe(self, test_cases: List[Dict[str, Any]]) -> pd.DataFrame:
        """准备优先级统计数据框"""
        priorities = {}
        for case in test_cases:
            priority = case.get("priority", "medium")
            priorities[priority] = priorities.get(priority, 0) + 1
        
        priority_data = [
            {"优先级": priority, "数量": count, "占比(%)": round(count / len(test_cases) * 100, 2)}
            for priority, count in priorities.items()
        ]
        
        return pd.DataFrame(priority_data)
    
    def _prepare_category_dataframe(self, test_cases: List[Dict[str, Any]]) -> pd.DataFrame:
        """准备分类统计数据框"""
        categories = {}
        for case in test_cases:
            category = case.get("category", "未分类")
            categories[category] = categories.get(category, 0) + 1
        
        category_data = [
            {"分类": category, "数量": count, "占比(%)": round(count / len(test_cases) * 100, 2)}
            for category, count in categories.items()
        ]
        
        return pd.DataFrame(category_data)
    
    def _format_list_for_excel(self, items: List[str]) -> str:
        """格式化列表为Excel单元格内容"""
        if not items:
            return ""
        return "\n".join([f"{i+1}. {item}" for i, item in enumerate(items)])
    
    def _format_main_sheet(self, worksheet, dataframe):
        """格式化主表样式"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            
            # 设置标题行样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center", vertical="center")
            
            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                worksheet.column_dimensions[column_letter].width = adjusted_width
                
        except ImportError:
            logger.warning("openpyxl样式模块未找到，跳过格式化")
    
    def _generate_testlink_xml(self, test_cases: List[Dict[str, Any]], testsuite_name: str) -> str:
        """生成TestLink XML内容"""
        xml_lines = [
            '<?xml version="1.0" encoding="UTF-8"?>',
            '<testsuite name="{}">'.format(testsuite_name)
        ]
        
        for case in test_cases:
            xml_lines.extend([
                '  <testcase name="{}">'.format(case.get("title", "")),
                '    <summary><![CDATA[{}]]></summary>'.format(case.get("description", "")),
                '    <preconditions><![CDATA[{}]]></preconditions>'.format(
                    "\n".join(case.get("preconditions", []))
                ),
                '    <steps>'
            ])
            
            steps = case.get("test_steps", [])
            expected_results = case.get("expected_results", [])
            
            for i, step in enumerate(steps):
                expected = expected_results[i] if i < len(expected_results) else ""
                xml_lines.extend([
                    '      <step>',
                    '        <step_number>{}</step_number>'.format(i + 1),
                    '        <actions><![CDATA[{}]]></actions>'.format(step),
                    '        <expectedresults><![CDATA[{}]]></expectedresults>'.format(expected),
                    '      </step>'
                ])
            
            xml_lines.extend([
                '    </steps>',
                '    <importance>{}</importance>'.format(
                    {"high": "3", "medium": "2", "low": "1"}.get(case.get("priority", "medium"), "2")
                ),
                '    <execution_type>1</execution_type>',  # 手工执行
                '  </testcase>'
            ])
        
        xml_lines.append('</testsuite>')
        
        return "\n".join(xml_lines)
