#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
需求分析工具
用于分析需求文档，提取测试点和业务规则
"""

import re
from typing import Any, Dict, List, Optional
from loguru import logger


class RequirementsAnalyzer:
    """需求分析器"""
    
    def __init__(self):
        self.keywords = {
            "action_words": ["输入", "点击", "选择", "提交", "验证", "显示", "跳转", "登录", "注册"],
            "condition_words": ["如果", "当", "则", "否则", "满足", "不满足", "正确", "错误"],
            "validation_words": ["验证", "检查", "确认", "校验", "提示", "错误", "成功", "失败"],
            "ui_elements": ["按钮", "输入框", "下拉框", "复选框", "单选框", "链接", "菜单", "页面"]
        }
    
    def analyze(self, requirements_text: str, document_type: str = "text") -> Dict[str, Any]:
        """
        分析需求文档
        
        参数:
        requirements_text: 需求文档内容
        document_type: 文档类型
        
        返回:
        分析结果字典
        """
        logger.info(f"开始分析需求文档，类型: {document_type}")
        
        result = {
            "test_points": [],
            "business_rules": [],
            "scenarios": [],
            "ui_elements": [],
            "user_roles": [],
            "data_requirements": []
        }
        
        # 根据文档类型选择不同的分析策略
        if document_type == "user_story":
            result = self._analyze_user_story(requirements_text)
        elif document_type == "acceptance_criteria":
            result = self._analyze_acceptance_criteria(requirements_text)
        else:
            result = self._analyze_general_requirements(requirements_text)
        
        logger.info(f"需求分析完成，提取到 {len(result['test_points'])} 个测试点")
        return result
    
    def _analyze_user_story(self, text: str) -> Dict[str, Any]:
        """分析用户故事格式的需求"""
        result = {
            "test_points": [],
            "business_rules": [],
            "scenarios": [],
            "ui_elements": [],
            "user_roles": [],
            "data_requirements": []
        }
        
        # 提取用户角色
        role_pattern = r"作为\s*([^，,]+)"
        roles = re.findall(role_pattern, text)
        result["user_roles"] = list(set(roles))
        
        # 提取功能需求
        function_pattern = r"我想要\s*([^，,。.]+)"
        functions = re.findall(function_pattern, text)
        
        # 提取价值/目标
        value_pattern = r"以便\s*([^，,。.]+)"
        values = re.findall(value_pattern, text)
        
        # 生成测试点
        for func in functions:
            result["test_points"].append({
                "type": "功能测试",
                "description": func.strip(),
                "priority": "high"
            })
        
        # 提取UI元素
        result["ui_elements"] = self._extract_ui_elements(text)
        
        return result
    
    def _analyze_acceptance_criteria(self, text: str) -> Dict[str, Any]:
        """分析验收标准"""
        result = {
            "test_points": [],
            "business_rules": [],
            "scenarios": [],
            "ui_elements": [],
            "user_roles": [],
            "data_requirements": []
        }
        
        # 按行分析验收标准
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 识别Given-When-Then格式
            if line.startswith(("Given", "假设", "给定")):
                result["scenarios"].append({
                    "type": "前置条件",
                    "description": line,
                    "category": "precondition"
                })
            elif line.startswith(("When", "当", "如果")):
                result["test_points"].append({
                    "type": "操作测试",
                    "description": line,
                    "priority": "high"
                })
            elif line.startswith(("Then", "那么", "则")):
                result["test_points"].append({
                    "type": "结果验证",
                    "description": line,
                    "priority": "high"
                })
            else:
                # 其他格式的验收标准
                result["test_points"].append({
                    "type": "验收测试",
                    "description": line,
                    "priority": "medium"
                })
        
        return result
    
    def _analyze_general_requirements(self, text: str) -> Dict[str, Any]:
        """分析一般需求文档"""
        result = {
            "test_points": [],
            "business_rules": [],
            "scenarios": [],
            "ui_elements": [],
            "user_roles": [],
            "data_requirements": []
        }
        
        # 按句子分析
        sentences = re.split(r'[。.！!？?；;]', text)
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            # 识别测试点
            if any(word in sentence for word in self.keywords["action_words"]):
                result["test_points"].append({
                    "type": "功能测试",
                    "description": sentence,
                    "priority": "medium"
                })
            
            # 识别业务规则
            if any(word in sentence for word in self.keywords["condition_words"]):
                result["business_rules"].append({
                    "description": sentence,
                    "type": "业务逻辑"
                })
            
            # 识别验证点
            if any(word in sentence for word in self.keywords["validation_words"]):
                result["test_points"].append({
                    "type": "验证测试",
                    "description": sentence,
                    "priority": "high"
                })
        
        # 提取UI元素
        result["ui_elements"] = self._extract_ui_elements(text)
        
        # 提取数据需求
        result["data_requirements"] = self._extract_data_requirements(text)
        
        return result
    
    def _extract_ui_elements(self, text: str) -> List[Dict[str, str]]:
        """提取UI元素"""
        ui_elements = []
        
        for element_type in self.keywords["ui_elements"]:
            pattern = rf"([^，,。.]*{element_type}[^，,。.]*)"
            matches = re.findall(pattern, text)
            for match in matches:
                ui_elements.append({
                    "type": element_type,
                    "description": match.strip(),
                    "name": match.strip()
                })
        
        return ui_elements
    
    def _extract_data_requirements(self, text: str) -> List[Dict[str, str]]:
        """提取数据需求"""
        data_requirements = []
        
        # 识别数据格式要求
        patterns = [
            (r"(\d+)位", "长度限制"),
            (r"必填|必须|不能为空", "必填字段"),
            (r"邮箱|email", "邮箱格式"),
            (r"手机号|电话", "电话格式"),
            (r"身份证", "身份证格式"),
            (r"密码.*(\d+)位", "密码长度")
        ]
        
        for pattern, data_type in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                data_requirements.append({
                    "type": data_type,
                    "description": f"检测到{data_type}要求",
                    "pattern": pattern
                })
        
        return data_requirements
    
    def validate_coverage(self, test_cases: List[Dict[str, Any]], requirements: List[str]) -> Dict[str, Any]:
        """验证测试覆盖率"""
        logger.info("开始验证测试覆盖率")
        
        covered_requirements = set()
        uncovered_requirements = []
        
        # 简单的关键词匹配来判断覆盖情况
        for requirement in requirements:
            requirement_keywords = set(re.findall(r'\w+', requirement.lower()))
            
            is_covered = False
            for test_case in test_cases:
                test_case_text = str(test_case).lower()
                test_case_keywords = set(re.findall(r'\w+', test_case_text))
                
                # 如果有足够的关键词重叠，认为已覆盖
                overlap = len(requirement_keywords & test_case_keywords)
                if overlap >= min(3, len(requirement_keywords) * 0.5):
                    covered_requirements.add(requirement)
                    is_covered = True
                    break
            
            if not is_covered:
                uncovered_requirements.append(requirement)
        
        coverage_percentage = (len(covered_requirements) / len(requirements) * 100) if requirements else 0
        
        return {
            "coverage_percentage": round(coverage_percentage, 2),
            "covered_count": len(covered_requirements),
            "total_requirements": len(requirements),
            "uncovered_requirements": uncovered_requirements,
            "covered_requirements": list(covered_requirements)
        }
