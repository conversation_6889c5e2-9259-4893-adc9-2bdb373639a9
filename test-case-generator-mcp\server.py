#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手工测试用例生成器 MCP 服务器
提供智能测试用例生成、分析和导出功能
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional
from fastmcp import FastMCP
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入配置和工具模块
from config import config
from tools.requirements_analyzer import RequirementsAnalyzer
from tools.test_generator import TestGenerator
from tools.formatter import TestCaseFormatter
from tools.exporter import TestCaseExporter

# 初始化MCP服务器
mcp = FastMCP(name="TestCaseGeneratorMCP")

# 初始化工具实例
requirements_analyzer = RequirementsAnalyzer()
test_generator = TestGenerator()
formatter = TestCaseFormatter()
exporter = TestCaseExporter()


@mcp.tool()
def analyze_requirements(requirements_text: str, document_type: str = "text") -> Dict[str, Any]:
    """
    分析需求文档，提取测试点和测试场景
    
    参数:
    requirements_text: 需求文档内容
    document_type: 文档类型 (text, user_story, acceptance_criteria)
    
    返回:
    包含测试点、业务规则、测试场景的分析结果
    """
    try:
        result = requirements_analyzer.analyze(requirements_text, document_type)
        logger.info(f"需求分析完成，提取到 {len(result.get('test_points', []))} 个测试点")
        return result
    except Exception as e:
        logger.error(f"需求分析失败: {str(e)}")
        return {"error": str(e), "test_points": [], "business_rules": [], "scenarios": []}


@mcp.tool()
def generate_functional_tests(
    feature_name: str,
    feature_description: str,
    acceptance_criteria: List[str],
    priority: str = "medium"
) -> Dict[str, Any]:
    """
    生成功能测试用例
    
    参数:
    feature_name: 功能名称
    feature_description: 功能描述
    acceptance_criteria: 验收标准列表
    priority: 优先级 (high, medium, low)
    
    返回:
    生成的功能测试用例列表
    """
    try:
        test_cases = test_generator.generate_functional_tests(
            feature_name, feature_description, acceptance_criteria, priority
        )
        logger.info(f"生成功能测试用例 {len(test_cases)} 个")
        return {"test_cases": test_cases, "count": len(test_cases)}
    except Exception as e:
        logger.error(f"功能测试用例生成失败: {str(e)}")
        return {"error": str(e), "test_cases": [], "count": 0}


@mcp.tool()
def generate_ui_tests(
    page_name: str,
    ui_elements: List[Dict[str, str]],
    user_interactions: List[str]
) -> Dict[str, Any]:
    """
    生成UI测试用例
    
    参数:
    page_name: 页面名称
    ui_elements: UI元素列表 [{"type": "button", "name": "登录按钮", "id": "login-btn"}]
    user_interactions: 用户交互列表
    
    返回:
    生成的UI测试用例列表
    """
    try:
        test_cases = test_generator.generate_ui_tests(page_name, ui_elements, user_interactions)
        logger.info(f"生成UI测试用例 {len(test_cases)} 个")
        return {"test_cases": test_cases, "count": len(test_cases)}
    except Exception as e:
        logger.error(f"UI测试用例生成失败: {str(e)}")
        return {"error": str(e), "test_cases": [], "count": 0}


@mcp.tool()
def generate_boundary_tests(
    input_fields: List[Dict[str, Any]],
    business_rules: List[str]
) -> Dict[str, Any]:
    """
    生成边界值测试用例
    
    参数:
    input_fields: 输入字段列表 [{"name": "age", "type": "number", "min": 0, "max": 120}]
    business_rules: 业务规则列表
    
    返回:
    生成的边界值测试用例列表
    """
    try:
        test_cases = test_generator.generate_boundary_tests(input_fields, business_rules)
        logger.info(f"生成边界值测试用例 {len(test_cases)} 个")
        return {"test_cases": test_cases, "count": len(test_cases)}
    except Exception as e:
        logger.error(f"边界值测试用例生成失败: {str(e)}")
        return {"error": str(e), "test_cases": [], "count": 0}


@mcp.tool()
def create_test_scenarios(
    workflow_description: str,
    user_roles: List[str],
    business_processes: List[str]
) -> Dict[str, Any]:
    """
    创建测试场景
    
    参数:
    workflow_description: 工作流程描述
    user_roles: 用户角色列表
    business_processes: 业务流程列表
    
    返回:
    生成的测试场景列表
    """
    try:
        scenarios = test_generator.create_test_scenarios(
            workflow_description, user_roles, business_processes
        )
        logger.info(f"创建测试场景 {len(scenarios)} 个")
        return {"scenarios": scenarios, "count": len(scenarios)}
    except Exception as e:
        logger.error(f"测试场景创建失败: {str(e)}")
        return {"error": str(e), "scenarios": [], "count": 0}


@mcp.tool()
def format_test_case(
    test_case_data: Dict[str, Any],
    format_type: str = "standard",
    template_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    格式化测试用例
    
    参数:
    test_case_data: 原始测试用例数据
    format_type: 格式类型 (standard, detailed, simple)
    template_name: 模板名称
    
    返回:
    格式化后的测试用例
    """
    try:
        formatted_case = formatter.format_test_case(test_case_data, format_type, template_name)
        logger.info("测试用例格式化完成")
        return {"formatted_test_case": formatted_case}
    except Exception as e:
        logger.error(f"测试用例格式化失败: {str(e)}")
        return {"error": str(e), "formatted_test_case": {}}


@mcp.tool()
def export_to_excel(
    test_cases: List[Dict[str, Any]],
    filename: str = "test_cases.xlsx",
    include_summary: bool = True
) -> Dict[str, Any]:
    """
    导出测试用例到Excel文件
    
    参数:
    test_cases: 测试用例列表
    filename: 文件名
    include_summary: 是否包含汇总信息
    
    返回:
    导出结果信息
    """
    try:
        result = exporter.export_to_excel(test_cases, filename, include_summary)
        logger.info(f"成功导出 {len(test_cases)} 个测试用例到 {filename}")
        return result
    except Exception as e:
        logger.error(f"Excel导出失败: {str(e)}")
        return {"error": str(e), "success": False}


@mcp.tool()
def validate_test_coverage(
    test_cases: List[Dict[str, Any]],
    requirements: List[str]
) -> Dict[str, Any]:
    """
    验证测试覆盖率
    
    参数:
    test_cases: 测试用例列表
    requirements: 需求列表
    
    返回:
    覆盖率分析结果
    """
    try:
        coverage_result = requirements_analyzer.validate_coverage(test_cases, requirements)
        logger.info(f"测试覆盖率分析完成，覆盖率: {coverage_result.get('coverage_percentage', 0)}%")
        return coverage_result
    except Exception as e:
        logger.error(f"测试覆盖率验证失败: {str(e)}")
        return {"error": str(e), "coverage_percentage": 0, "uncovered_requirements": []}


def main():
    """启动MCP服务器"""
    # 设置日志
    log_config = config.get_log_config()
    logger.remove()
    for handler in log_config["handlers"]:
        logger.add(**handler)

    logger.info("启动手工测试用例生成器 MCP 服务器...")
    logger.info(f"服务器地址: {config.SERVER_HOST}:{config.SERVER_PORT}")
    logger.info(f"传输协议: {config.SERVER_TRANSPORT}")

    mcp.run(
        transport=config.SERVER_TRANSPORT,
        host=config.SERVER_HOST,
        port=config.SERVER_PORT
    )


if __name__ == "__main__":
    main()
