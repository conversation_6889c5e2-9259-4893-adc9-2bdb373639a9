[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "test-case-generator-mcp"
version = "1.0.0"
description = "智能手工测试用例生成器 MCP 服务"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "mcp>=1.0.0",
    "fastmcp>=0.9.0",
    "pandas>=2.0.0",
    "openpyxl>=3.1.0",
    "jinja2>=3.1.0",
    "pydantic>=2.0.0",
    "loguru>=0.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]
nlp = [
    "jieba>=0.42.1",
    "nltk>=3.8.1",
    "textblob>=0.17.1",
]
docs = [
    "python-docx>=0.8.11",
    "PyPDF2>=3.0.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/test-case-generator-mcp"
Repository = "https://github.com/yourusername/test-case-generator-mcp"
Issues = "https://github.com/yourusername/test-case-generator-mcp/issues"

[project.scripts]
test-case-mcp = "test_case_generator.server:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["test_case_generator*"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
