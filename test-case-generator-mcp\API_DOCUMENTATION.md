# 手工测试用例生成器 MCP API 文档

## 概述

本文档描述了手工测试用例生成器 MCP 服务器提供的所有工具和API接口。

## 服务器信息

- **默认地址**: `http://1********:8002/sse`
- **传输协议**: Server-Sent Events (SSE)
- **数据格式**: JSON

## 工具列表

### 1. analyze_requirements

分析需求文档，提取测试点和测试场景。

**参数:**
- `requirements_text` (string, 必需): 需求文档内容
- `document_type` (string, 可选): 文档类型，默认为 "text"
  - `"text"`: 一般文本需求
  - `"user_story"`: 用户故事格式
  - `"acceptance_criteria"`: 验收标准格式

**返回值:**
```json
{
  "test_points": [
    {
      "type": "功能测试",
      "description": "测试点描述",
      "priority": "high"
    }
  ],
  "business_rules": [
    {
      "description": "业务规则描述",
      "type": "业务逻辑"
    }
  ],
  "scenarios": [],
  "ui_elements": [
    {
      "type": "按钮",
      "description": "UI元素描述",
      "name": "元素名称"
    }
  ],
  "user_roles": ["用户角色1", "用户角色2"],
  "data_requirements": []
}
```

**示例调用:**
```json
{
  "tool": "analyze_requirements",
  "arguments": {
    "requirements_text": "作为用户，我想要登录系统，以便访问个人信息",
    "document_type": "user_story"
  }
}
```

### 2. generate_functional_tests

生成功能测试用例。

**参数:**
- `feature_name` (string, 必需): 功能名称
- `feature_description` (string, 必需): 功能描述
- `acceptance_criteria` (array, 必需): 验收标准列表
- `priority` (string, 可选): 优先级，默认为 "medium"
  - `"high"`: 高优先级
  - `"medium"`: 中优先级
  - `"low"`: 低优先级

**返回值:**
```json
{
  "test_cases": [
    {
      "id": "uuid",
      "title": "测试用例标题",
      "description": "测试用例描述",
      "preconditions": ["前置条件1", "前置条件2"],
      "test_steps": ["步骤1", "步骤2"],
      "expected_results": ["预期结果1", "预期结果2"],
      "priority": "high",
      "category": "功能测试",
      "tags": ["标签1", "标签2"],
      "estimated_time": 5,
      "created_at": "2024-01-01T00:00:00",
      "status": "draft"
    }
  ],
  "count": 3
}
```

**示例调用:**
```json
{
  "tool": "generate_functional_tests",
  "arguments": {
    "feature_name": "用户登录",
    "feature_description": "用户通过用户名和密码登录系统",
    "acceptance_criteria": [
      "正确用户名密码可以登录",
      "错误密码显示错误信息"
    ],
    "priority": "high"
  }
}
```

### 3. generate_ui_tests

生成UI测试用例。

**参数:**
- `page_name` (string, 必需): 页面名称
- `ui_elements` (array, 必需): UI元素列表
  - `type` (string): 元素类型 (button, input, select等)
  - `name` (string): 元素名称
  - `id` (string, 可选): 元素ID
- `user_interactions` (array, 必需): 用户交互列表

**返回值:**
```json
{
  "test_cases": [
    {
      "id": "uuid",
      "title": "UI测试用例标题",
      "description": "UI测试描述",
      "test_steps": ["UI测试步骤"],
      "expected_results": ["UI预期结果"],
      "category": "UI测试"
    }
  ],
  "count": 5
}
```

**示例调用:**
```json
{
  "tool": "generate_ui_tests",
  "arguments": {
    "page_name": "登录页面",
    "ui_elements": [
      {"type": "input", "name": "用户名输入框", "id": "username"},
      {"type": "button", "name": "登录按钮", "id": "login-btn"}
    ],
    "user_interactions": ["用户登录流程", "表单验证"]
  }
}
```

### 4. generate_boundary_tests

生成边界值测试用例。

**参数:**
- `input_fields` (array, 必需): 输入字段列表
  - `name` (string): 字段名称
  - `type` (string): 字段类型 (number, text, password等)
  - `min` (number, 可选): 最小值
  - `max` (number, 可选): 最大值
- `business_rules` (array, 必需): 业务规则列表

**返回值:**
```json
{
  "test_cases": [
    {
      "id": "uuid",
      "title": "边界值测试标题",
      "description": "边界值测试描述",
      "category": "边界值测试",
      "test_steps": ["边界值测试步骤"],
      "expected_results": ["边界值预期结果"]
    }
  ],
  "count": 4
}
```

### 5. create_test_scenarios

创建测试场景。

**参数:**
- `workflow_description` (string, 必需): 工作流程描述
- `user_roles` (array, 必需): 用户角色列表
- `business_processes` (array, 必需): 业务流程列表

**返回值:**
```json
{
  "scenarios": [
    {
      "id": "uuid",
      "name": "场景名称",
      "description": "场景描述",
      "user_role": "用户角色",
      "business_process": "业务流程",
      "workflow_steps": ["流程步骤"],
      "test_data_requirements": ["测试数据需求"],
      "expected_outcomes": ["预期结果"],
      "estimated_duration": 15
    }
  ],
  "count": 6
}
```

### 6. format_test_case

格式化测试用例。

**参数:**
- `test_case_data` (object, 必需): 原始测试用例数据
- `format_type` (string, 可选): 格式类型，默认为 "standard"
  - `"standard"`: 标准格式
  - `"detailed"`: 详细格式
  - `"simple"`: 简单格式
  - `"testlink"`: TestLink格式
  - `"jira"`: Jira格式
- `template_name` (string, 可选): 自定义模板名称

**返回值:**
```json
{
  "formatted_test_case": {
    "测试用例ID": "TC_001",
    "测试用例标题": "格式化后的标题",
    "测试描述": "格式化后的描述",
    "前置条件": "• 条件1\n• 条件2",
    "测试步骤": "1. 步骤1\n2. 步骤2",
    "预期结果": "• 结果1\n• 结果2"
  }
}
```

### 7. export_to_excel

导出测试用例到Excel文件。

**参数:**
- `test_cases` (array, 必需): 测试用例列表
- `filename` (string, 可选): 文件名，默认为 "test_cases.xlsx"
- `include_summary` (boolean, 可选): 是否包含汇总信息，默认为 true

**返回值:**
```json
{
  "success": true,
  "filepath": "/path/to/exports/test_cases.xlsx",
  "filename": "test_cases.xlsx",
  "total_cases": 10,
  "file_size": 15360,
  "created_at": "2024-01-01T00:00:00"
}
```

### 8. validate_test_coverage

验证测试覆盖率。

**参数:**
- `test_cases` (array, 必需): 测试用例列表
- `requirements` (array, 必需): 需求列表

**返回值:**
```json
{
  "coverage_percentage": 85.5,
  "covered_count": 17,
  "total_requirements": 20,
  "uncovered_requirements": ["未覆盖需求1", "未覆盖需求2"],
  "covered_requirements": ["已覆盖需求1", "已覆盖需求2"]
}
```

## 错误处理

所有工具在出错时都会返回包含错误信息的响应：

```json
{
  "error": "错误描述信息",
  "success": false
}
```

## 使用示例

### Python客户端示例

```python
import asyncio
from fastmcp import Client

async def example():
    async with Client("http://1********:8002/sse") as client:
        # 分析需求
        result = await client.call_tool(
            "analyze_requirements",
            {
                "requirements_text": "用户登录功能需求",
                "document_type": "text"
            }
        )
        print(result)
        
        # 生成测试用例
        test_cases = await client.call_tool(
            "generate_functional_tests",
            {
                "feature_name": "用户登录",
                "feature_description": "用户登录系统",
                "acceptance_criteria": ["成功登录", "错误处理"],
                "priority": "high"
            }
        )
        print(test_cases)

asyncio.run(example())
```

## 配置选项

服务器支持以下环境变量配置：

- `MCP_HOST`: 服务器主机地址 (默认: 1********)
- `MCP_PORT`: 服务器端口 (默认: 8002)
- `MCP_TRANSPORT`: 传输协议 (默认: sse)
- `LOG_LEVEL`: 日志级别 (默认: INFO)

## 文件导出

导出的文件保存在 `exports/` 目录下，支持以下格式：

- **Excel (.xlsx)**: 包含多个工作表的详细测试用例
- **CSV (.csv)**: 简单的表格格式
- **JSON (.json)**: 结构化数据格式
- **XML (.xml)**: TestLink兼容格式

## 限制和注意事项

1. 单次请求最多处理100个测试用例
2. 导出文件大小限制为50MB
3. 请求超时时间为30秒
4. 并发请求限制为10个

## 版本信息

- **当前版本**: 1.0.0
- **MCP协议版本**: 1.0+
- **Python版本要求**: 3.8+
