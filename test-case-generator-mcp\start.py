#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本
提供便捷的服务器启动和管理功能
"""

import argparse
import asyncio
import sys
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from server import main as server_main


def setup_logging():
    """设置日志"""
    logger.remove()  # 移除默认处理器
    
    log_config = config.get_log_config()
    for handler in log_config["handlers"]:
        logger.add(**handler)
    
    logger.info("日志系统初始化完成")


def check_dependencies():
    """检查依赖"""
    required_packages = [
        "fastmcp",
        "pandas", 
        "openpyxl",
        "loguru"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少必要的依赖包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    logger.info("依赖检查通过")
    return True


def show_server_info():
    """显示服务器信息"""
    print("🚀 手工测试用例生成器 MCP 服务器")
    print("=" * 50)
    print(f"服务器地址: {config.SERVER_HOST}:{config.SERVER_PORT}")
    print(f"传输协议: {config.SERVER_TRANSPORT}")
    print(f"项目根目录: {config.PROJECT_ROOT}")
    print(f"导出目录: {config.EXPORTS_DIR}")
    print(f"日志目录: {config.LOGS_DIR}")
    print("=" * 50)


def show_available_tools():
    """显示可用工具"""
    tools = [
        ("analyze_requirements", "分析需求文档，提取测试点"),
        ("generate_functional_tests", "生成功能测试用例"),
        ("generate_ui_tests", "生成UI测试用例"),
        ("generate_boundary_tests", "生成边界值测试用例"),
        ("create_test_scenarios", "创建测试场景"),
        ("format_test_case", "格式化测试用例"),
        ("export_to_excel", "导出Excel格式"),
        ("validate_test_coverage", "验证测试覆盖率")
    ]
    
    print("\n📋 可用工具:")
    for tool_name, description in tools:
        print(f"  • {tool_name}: {description}")
    print()


def run_server():
    """运行服务器"""
    try:
        logger.info("启动MCP服务器...")
        server_main()
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器运行出错: {str(e)}")
        sys.exit(1)


def run_client_demo():
    """运行客户端演示"""
    try:
        from client import TestCaseGeneratorClient
        
        async def demo():
            client = TestCaseGeneratorClient()
            await client.run_demo()
        
        logger.info("启动客户端演示...")
        asyncio.run(demo())
        
    except ImportError:
        logger.error("客户端模块未找到")
    except Exception as e:
        logger.error(f"客户端演示运行出错: {str(e)}")


def run_examples():
    """运行示例"""
    try:
        from examples.basic_usage import main as examples_main
        
        logger.info("运行基本使用示例...")
        asyncio.run(examples_main())
        
    except ImportError:
        logger.error("示例模块未找到")
    except Exception as e:
        logger.error(f"示例运行出错: {str(e)}")


def run_tests():
    """运行测试"""
    try:
        import subprocess
        result = subprocess.run(
            ["python", "-m", "pytest", "tests/", "-v"],
            cwd=project_root,
            capture_output=True,
            text=True
        )
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        if result.returncode == 0:
            logger.info("所有测试通过")
        else:
            logger.error("测试失败")
            sys.exit(1)
            
    except FileNotFoundError:
        logger.error("pytest未安装，请运行: pip install pytest")
    except Exception as e:
        logger.error(f"运行测试出错: {str(e)}")


def show_status():
    """显示系统状态"""
    print("📊 系统状态:")
    print(f"  • Python版本: {sys.version}")
    print(f"  • 项目路径: {config.PROJECT_ROOT}")
    print(f"  • 配置文件: 已加载")
    print(f"  • 日志级别: {config.LOG_LEVEL}")
    
    # 检查目录
    directories = [
        ("模板目录", config.TEMPLATES_DIR),
        ("导出目录", config.EXPORTS_DIR),
        ("日志目录", config.LOGS_DIR),
        ("示例目录", config.EXAMPLES_DIR)
    ]
    
    print("  • 目录状态:")
    for name, path in directories:
        status = "✓" if path.exists() else "✗"
        print(f"    {status} {name}: {path}")
    
    # 检查依赖
    print("  • 依赖状态:", "✓ 正常" if check_dependencies() else "✗ 缺少依赖")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="手工测试用例生成器 MCP 服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python start.py server          # 启动服务器
  python start.py demo            # 运行客户端演示
  python start.py examples        # 运行使用示例
  python start.py test            # 运行测试
  python start.py status          # 显示系统状态
        """
    )
    
    parser.add_argument(
        "command",
        choices=["server", "demo", "examples", "test", "status"],
        help="要执行的命令"
    )
    
    parser.add_argument(
        "--host",
        default=config.SERVER_HOST,
        help=f"服务器主机地址 (默认: {config.SERVER_HOST})"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=config.SERVER_PORT,
        help=f"服务器端口 (默认: {config.SERVER_PORT})"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default=config.LOG_LEVEL,
        help=f"日志级别 (默认: {config.LOG_LEVEL})"
    )
    
    parser.add_argument(
        "--no-check",
        action="store_true",
        help="跳过依赖检查"
    )
    
    args = parser.parse_args()
    
    # 更新配置
    config.SERVER_HOST = args.host
    config.SERVER_PORT = args.port
    config.LOG_LEVEL = args.log_level
    
    # 设置日志
    setup_logging()
    
    # 显示服务器信息
    if args.command == "server":
        show_server_info()
        show_available_tools()
    
    # 检查依赖（除非跳过）
    if not args.no_check and not check_dependencies():  
        sys.exit(1)
    
    # 执行命令
    if args.command == "server":
        run_server()
    elif args.command == "demo":
        run_client_demo()
    elif args.command == "examples":
        run_examples()
    elif args.command == "test":
        run_tests()
    elif args.command == "status":
        show_status()


if __name__ == "__main__":
    main()
