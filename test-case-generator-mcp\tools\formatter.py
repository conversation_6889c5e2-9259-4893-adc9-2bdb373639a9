#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用例格式化工具
用于将测试用例格式化为不同的标准格式
"""

from typing import Any, Dict, List, Optional
from datetime import datetime
from loguru import logger


class TestCaseFormatter:
    """测试用例格式化器"""
    
    def __init__(self):
        self.templates = {
            "standard": self._standard_template,
            "detailed": self._detailed_template,
            "simple": self._simple_template,
            "testlink": self._testlink_template,
            "jira": self._jira_template
        }
    
    def format_test_case(
        self,
        test_case_data: Dict[str, Any],
        format_type: str = "standard",
        template_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        格式化测试用例
        
        参数:
        test_case_data: 原始测试用例数据
        format_type: 格式类型
        template_name: 自定义模板名称
        
        返回:
        格式化后的测试用例
        """
        logger.info(f"格式化测试用例: {format_type}")
        
        if template_name and template_name in self.templates:
            formatter = self.templates[template_name]
        elif format_type in self.templates:
            formatter = self.templates[format_type]
        else:
            logger.warning(f"未知格式类型: {format_type}，使用标准格式")
            formatter = self.templates["standard"]
        
        return formatter(test_case_data)
    
    def format_test_suite(
        self,
        test_cases: List[Dict[str, Any]],
        suite_name: str,
        format_type: str = "standard"
    ) -> Dict[str, Any]:
        """格式化测试套件"""
        logger.info(f"格式化测试套件: {suite_name}")
        
        formatted_cases = []
        for test_case in test_cases:
            formatted_case = self.format_test_case(test_case, format_type)
            formatted_cases.append(formatted_case)
        
        suite = {
            "suite_name": suite_name,
            "total_cases": len(formatted_cases),
            "created_at": datetime.now().isoformat(),
            "format_type": format_type,
            "test_cases": formatted_cases,
            "summary": self._generate_suite_summary(formatted_cases)
        }
        
        return suite
    
    def _standard_template(self, test_case_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准格式模板"""
        return {
            "测试用例ID": test_case_data.get("id", ""),
            "测试用例标题": test_case_data.get("title", ""),
            "测试描述": test_case_data.get("description", ""),
            "前置条件": self._format_list(test_case_data.get("preconditions", [])),
            "测试步骤": self._format_steps(test_case_data.get("test_steps", [])),
            "预期结果": self._format_list(test_case_data.get("expected_results", [])),
            "优先级": test_case_data.get("priority", "medium"),
            "测试分类": test_case_data.get("category", ""),
            "标签": ", ".join(test_case_data.get("tags", [])),
            "预估时间": f"{test_case_data.get('estimated_time', 5)}分钟",
            "创建时间": test_case_data.get("created_at", ""),
            "状态": test_case_data.get("status", "draft")
        }
    
    def _detailed_template(self, test_case_data: Dict[str, Any]) -> Dict[str, Any]:
        """详细格式模板"""
        standard = self._standard_template(test_case_data)
        
        # 添加详细信息
        detailed = standard.copy()
        detailed.update({
            "测试目标": test_case_data.get("test_objective", "验证功能正确性"),
            "测试环境": test_case_data.get("test_environment", "测试环境"),
            "测试数据": self._format_test_data(test_case_data.get("test_data", {})),
            "风险等级": test_case_data.get("risk_level", "medium"),
            "依赖关系": self._format_list(test_case_data.get("dependencies", [])),
            "注意事项": test_case_data.get("notes", ""),
            "相关需求": test_case_data.get("related_requirements", ""),
            "执行频率": test_case_data.get("execution_frequency", "每次发布"),
            "自动化可能性": test_case_data.get("automation_potential", "待评估")
        })
        
        return detailed
    
    def _simple_template(self, test_case_data: Dict[str, Any]) -> Dict[str, Any]:
        """简单格式模板"""
        return {
            "ID": test_case_data.get("id", "")[:8],  # 短ID
            "标题": test_case_data.get("title", ""),
            "步骤": " -> ".join([
                step.split(". ", 1)[-1] if ". " in step else step 
                for step in test_case_data.get("test_steps", [])
            ]),
            "预期": " | ".join(test_case_data.get("expected_results", [])),
            "优先级": test_case_data.get("priority", "medium"),
            "分类": test_case_data.get("category", "")
        }
    
    def _testlink_template(self, test_case_data: Dict[str, Any]) -> Dict[str, Any]:
        """TestLink格式模板"""
        return {
            "testcase_name": test_case_data.get("title", ""),
            "summary": test_case_data.get("description", ""),
            "preconditions": "\n".join(test_case_data.get("preconditions", [])),
            "steps": self._format_testlink_steps(
                test_case_data.get("test_steps", []),
                test_case_data.get("expected_results", [])
            ),
            "importance": self._convert_priority_to_importance(test_case_data.get("priority", "medium")),
            "execution_type": "1",  # 手工执行
            "estimated_exec_duration": test_case_data.get("estimated_time", 5),
            "status": "1",  # 最终版本
            "keywords": ",".join(test_case_data.get("tags", [])),
            "author_login": "test_generator",
            "creation_ts": test_case_data.get("created_at", datetime.now().isoformat())
        }
    
    def _jira_template(self, test_case_data: Dict[str, Any]) -> Dict[str, Any]:
        """Jira格式模板"""
        return {
            "summary": test_case_data.get("title", ""),
            "description": self._format_jira_description(test_case_data),
            "priority": self._convert_priority_to_jira(test_case_data.get("priority", "medium")),
            "labels": test_case_data.get("tags", []),
            "components": [test_case_data.get("category", "")],
            "customfield_testtype": "Manual",
            "customfield_preconditions": "\n".join(test_case_data.get("preconditions", [])),
            "customfield_teststeps": self._format_jira_steps(
                test_case_data.get("test_steps", []),
                test_case_data.get("expected_results", [])
            ),
            "timeoriginalestimate": f"{test_case_data.get('estimated_time', 5)}m"
        }
    
    def _format_list(self, items: List[str]) -> str:
        """格式化列表为字符串"""
        if not items:
            return ""
        return "\n".join([f"• {item}" for item in items])
    
    def _format_steps(self, steps: List[str]) -> str:
        """格式化测试步骤"""
        if not steps:
            return ""
        
        formatted_steps = []
        for i, step in enumerate(steps, 1):
            # 如果步骤已经有编号，保持原样；否则添加编号
            if step.strip().startswith(f"{i}."):
                formatted_steps.append(step)
            else:
                # 移除可能存在的其他编号格式
                clean_step = step.strip()
                if ". " in clean_step and clean_step.split(". ", 1)[0].isdigit():
                    clean_step = clean_step.split(". ", 1)[1]
                formatted_steps.append(f"{i}. {clean_step}")
        
        return "\n".join(formatted_steps)
    
    def _format_test_data(self, test_data: Dict[str, Any]) -> str:
        """格式化测试数据"""
        if not test_data:
            return "无特殊测试数据要求"
        
        formatted_data = []
        for key, value in test_data.items():
            formatted_data.append(f"{key}: {value}")
        
        return "\n".join(formatted_data)
    
    def _format_testlink_steps(self, steps: List[str], expected_results: List[str]) -> str:
        """格式化TestLink步骤格式"""
        formatted_steps = []
        
        for i, step in enumerate(steps):
            expected = expected_results[i] if i < len(expected_results) else ""
            formatted_steps.append(f"<step><step_number>{i+1}</step_number><actions>{step}</actions><expectedresults>{expected}</expectedresults></step>")
        
        return "\n".join(formatted_steps)
    
    def _format_jira_description(self, test_case_data: Dict[str, Any]) -> str:
        """格式化Jira描述"""
        description_parts = []
        
        if test_case_data.get("description"):
            description_parts.append(f"*测试描述:*\n{test_case_data['description']}")
        
        if test_case_data.get("preconditions"):
            description_parts.append(f"*前置条件:*\n{self._format_list(test_case_data['preconditions'])}")
        
        return "\n\n".join(description_parts)
    
    def _format_jira_steps(self, steps: List[str], expected_results: List[str]) -> str:
        """格式化Jira测试步骤"""
        formatted_steps = []
        
        for i, step in enumerate(steps):
            expected = expected_results[i] if i < len(expected_results) else ""
            formatted_steps.append(f"*步骤 {i+1}:* {step}\n*预期结果:* {expected}")
        
        return "\n\n".join(formatted_steps)
    
    def _convert_priority_to_importance(self, priority: str) -> str:
        """转换优先级为TestLink重要性"""
        priority_map = {
            "high": "3",
            "medium": "2", 
            "low": "1"
        }
        return priority_map.get(priority.lower(), "2")
    
    def _convert_priority_to_jira(self, priority: str) -> str:
        """转换优先级为Jira优先级"""
        priority_map = {
            "high": "High",
            "medium": "Medium",
            "low": "Low"
        }
        return priority_map.get(priority.lower(), "Medium")
    
    def _generate_suite_summary(self, test_cases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成测试套件摘要"""
        if not test_cases:
            return {"total": 0, "by_priority": {}, "by_category": {}}
        
        priority_count = {}
        category_count = {}
        
        for case in test_cases:
            # 统计优先级
            priority = case.get("优先级", case.get("priority", "medium"))
            priority_count[priority] = priority_count.get(priority, 0) + 1
            
            # 统计分类
            category = case.get("测试分类", case.get("category", "未分类"))
            category_count[category] = category_count.get(category, 0) + 1
        
        return {
            "total": len(test_cases),
            "by_priority": priority_count,
            "by_category": category_count,
            "estimated_total_time": sum([
                int(str(case.get("预估时间", case.get("estimated_time", "5分钟"))).replace("分钟", ""))
                for case in test_cases
            ])
        }
