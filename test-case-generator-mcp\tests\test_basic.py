#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试
"""

import pytest
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tools.requirements_analyzer import RequirementsAnalyzer
from tools.test_generator import TestGenerator
from tools.formatter import TestCaseFormatter
from tools.exporter import TestCaseExporter


class TestRequirementsAnalyzer:
    """需求分析器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = RequirementsAnalyzer()
    
    def test_analyze_user_story(self):
        """测试用户故事分析"""
        user_story = """
        作为系统用户，我想要登录系统，以便访问个人信息。
        """
        
        result = self.analyzer.analyze(user_story, "user_story")
        
        assert isinstance(result, dict)
        assert "test_points" in result
        assert "user_roles" in result
        assert len(result["user_roles"]) > 0
        assert "系统用户" in result["user_roles"]
    
    def test_analyze_general_requirements(self):
        """测试一般需求分析"""
        requirements = """
        用户点击登录按钮后，系统验证用户名和密码。
        如果验证成功，则跳转到主页面。
        """
        
        result = self.analyzer.analyze(requirements, "text")
        
        assert isinstance(result, dict)
        assert "test_points" in result
        assert "business_rules" in result
        assert len(result["test_points"]) > 0
    
    def test_extract_ui_elements(self):
        """测试UI元素提取"""
        text = "用户点击登录按钮，在用户名输入框中输入用户名"
        
        ui_elements = self.analyzer._extract_ui_elements(text)
        
        assert isinstance(ui_elements, list)
        assert len(ui_elements) > 0
        
        # 检查是否提取到按钮和输入框
        element_types = [elem["type"] for elem in ui_elements]
        assert "按钮" in element_types or "输入框" in element_types


class TestTestGenerator:
    """测试用例生成器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.generator = TestGenerator()
    
    def test_generate_functional_tests(self):
        """测试功能测试用例生成"""
        result = self.generator.generate_functional_tests(
            feature_name="用户登录",
            feature_description="用户通过用户名密码登录",
            acceptance_criteria=["正确用户名密码可以登录", "错误密码显示错误信息"],
            priority="high"
        )
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # 检查测试用例结构
        test_case = result[0]
        assert "id" in test_case
        assert "title" in test_case
        assert "test_steps" in test_case
        assert "expected_results" in test_case
        assert test_case["priority"] == "high"
    
    def test_generate_ui_tests(self):
        """测试UI测试用例生成"""
        ui_elements = [
            {"type": "button", "name": "登录按钮", "id": "login-btn"}
        ]
        user_interactions = ["用户登录"]
        
        result = self.generator.generate_ui_tests(
            page_name="登录页面",
            ui_elements=ui_elements,
            user_interactions=user_interactions
        )
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # 检查是否包含页面加载测试
        titles = [case["title"] for case in result]
        assert any("页面加载" in title for title in titles)
    
    def test_create_base_test_case(self):
        """测试基础测试用例创建"""
        test_case = self.generator._create_base_test_case(
            title="测试标题",
            description="测试描述",
            category="功能测试",
            priority="medium"
        )
        
        assert isinstance(test_case, dict)
        assert test_case["title"] == "测试标题"
        assert test_case["description"] == "测试描述"
        assert test_case["category"] == "功能测试"
        assert test_case["priority"] == "medium"
        assert "id" in test_case
        assert "created_at" in test_case


class TestTestCaseFormatter:
    """测试用例格式化器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.formatter = TestCaseFormatter()
        self.sample_test_case = {
            "id": "test-001",
            "title": "用户登录测试",
            "description": "验证用户登录功能",
            "category": "功能测试",
            "priority": "high",
            "preconditions": ["系统可用", "用户账号存在"],
            "test_steps": ["1. 打开登录页面", "2. 输入用户名密码", "3. 点击登录"],
            "expected_results": ["页面正常显示", "输入成功", "登录成功"],
            "tags": ["登录", "功能"],
            "estimated_time": 5
        }
    
    def test_standard_format(self):
        """测试标准格式化"""
        result = self.formatter.format_test_case(
            self.sample_test_case,
            format_type="standard"
        )
        
        assert isinstance(result, dict)
        assert "测试用例ID" in result
        assert "测试用例标题" in result
        assert "测试步骤" in result
        assert "预期结果" in result
        assert result["测试用例标题"] == "用户登录测试"
    
    def test_simple_format(self):
        """测试简单格式化"""
        result = self.formatter.format_test_case(
            self.sample_test_case,
            format_type="simple"
        )
        
        assert isinstance(result, dict)
        assert "ID" in result
        assert "标题" in result
        assert "步骤" in result
        assert "预期" in result
    
    def test_format_test_suite(self):
        """测试测试套件格式化"""
        test_cases = [self.sample_test_case]
        
        result = self.formatter.format_test_suite(
            test_cases=test_cases,
            suite_name="登录测试套件",
            format_type="standard"
        )
        
        assert isinstance(result, dict)
        assert "suite_name" in result
        assert "total_cases" in result
        assert "test_cases" in result
        assert "summary" in result
        assert result["suite_name"] == "登录测试套件"
        assert result["total_cases"] == 1


class TestTestCaseExporter:
    """测试用例导出器测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.exporter = TestCaseExporter(output_dir="test_exports")
        self.sample_test_cases = [
            {
                "id": "test-001",
                "title": "用户登录测试",
                "description": "验证用户登录功能",
                "category": "功能测试",
                "priority": "high",
                "preconditions": ["系统可用"],
                "test_steps": ["1. 打开登录页面", "2. 输入用户名密码"],
                "expected_results": ["页面正常显示", "登录成功"],
                "estimated_time": 5,
                "status": "draft"
            }
        ]
    
    def test_prepare_main_dataframe(self):
        """测试主数据框准备"""
        df = self.exporter._prepare_main_dataframe(self.sample_test_cases)
        
        assert not df.empty
        assert len(df) == 1
        assert "测试用例ID" in df.columns
        assert "标题" in df.columns
        assert "测试步骤" in df.columns
    
    def test_prepare_summary_dataframe(self):
        """测试汇总数据框准备"""
        df = self.exporter._prepare_summary_dataframe(self.sample_test_cases)
        
        assert not df.empty
        assert "项目" in df.columns
        assert "数值" in df.columns
        
        # 检查是否包含总数统计
        items = df["项目"].tolist()
        assert "测试用例总数" in items
    
    def test_export_to_json(self):
        """测试JSON导出"""
        result = self.exporter.export_to_json(
            self.sample_test_cases,
            filename="test_cases.json"
        )
        
        assert isinstance(result, dict)
        assert result.get("success") is True
        assert "filepath" in result
        assert "total_cases" in result
        assert result["total_cases"] == 1
    
    def test_format_list_for_excel(self):
        """测试Excel列表格式化"""
        items = ["步骤1", "步骤2", "步骤3"]
        result = self.exporter._format_list_for_excel(items)
        
        assert isinstance(result, str)
        assert "1. 步骤1" in result
        assert "2. 步骤2" in result
        assert "3. 步骤3" in result


class TestIntegration:
    """集成测试"""
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 1. 需求分析
        analyzer = RequirementsAnalyzer()
        requirements = "用户点击登录按钮进行登录验证"
        analysis_result = analyzer.analyze(requirements, "text")
        
        assert "test_points" in analysis_result
        
        # 2. 测试用例生成
        generator = TestGenerator()
        test_cases = generator.generate_functional_tests(
            feature_name="用户登录",
            feature_description="用户登录功能",
            acceptance_criteria=["用户可以成功登录"],
            priority="high"
        )
        
        assert len(test_cases) > 0
        
        # 3. 格式化
        formatter = TestCaseFormatter()
        formatted_case = formatter.format_test_case(
            test_cases[0],
            format_type="standard"
        )
        
        assert "测试用例标题" in formatted_case
        
        # 4. 导出
        exporter = TestCaseExporter(output_dir="test_exports")
        export_result = exporter.export_to_json(
            test_cases,
            filename="integration_test.json"
        )
        
        assert export_result.get("success") is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
