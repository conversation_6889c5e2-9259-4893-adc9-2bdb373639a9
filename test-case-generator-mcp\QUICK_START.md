# 快速开始指南

## 🚀 5分钟快速体验

### 1. 安装依赖

```bash
# 克隆或下载项目到本地
cd test-case-generator-mcp

# 安装Python依赖
pip install -r requirements.txt
```

### 2. 启动服务器

```bash
# 方式1: 使用启动脚本（推荐）
python start.py server

# 方式2: 直接启动
python server.py
```

看到以下输出表示启动成功：
```
🚀 手工测试用例生成器 MCP 服务器
==================================================
服务器地址: 127.0.0.1:8002
传输协议: sse
==================================================

📋 可用工具:
  • analyze_requirements: 分析需求文档，提取测试点
  • generate_functional_tests: 生成功能测试用例
  • generate_ui_tests: 生成UI测试用例
  ...
```

### 3. 运行演示

打开新的终端窗口：

```bash
# 运行完整演示
python start.py demo

# 或者运行基本使用示例
python start.py examples
```

### 4. 交互式体验

```bash
# 启动交互模式
python client.py
# 选择 2 (交互模式)
```

## 📝 基本使用

### 需求分析示例

```python
import asyncio
from fastmcp import Client

async def analyze_requirements():
    async with Client("http://127.0.0.1:8002/sse") as client:
        result = await client.call_tool(
            "analyze_requirements",
            {
                "requirements_text": """
                作为电商用户，我想要搜索商品，以便找到需要的产品。
                验收标准：
                1. 用户可以输入关键词搜索
                2. 系统返回相关商品列表
                3. 支持按价格筛选
                """,
                "document_type": "user_story"
            }
        )
        print("需求分析结果:")
        print(result)

asyncio.run(analyze_requirements())
```

### 生成测试用例示例

```python
async def generate_tests():
    async with Client("http://127.0.0.1:8002/sse") as client:
        result = await client.call_tool(
            "generate_functional_tests",
            {
                "feature_name": "商品搜索",
                "feature_description": "用户搜索商品功能",
                "acceptance_criteria": [
                    "输入关键词可以搜索",
                    "显示搜索结果列表",
                    "支持结果筛选"
                ],
                "priority": "high"
            }
        )
        print("生成的测试用例:")
        for i, test_case in enumerate(result["test_cases"], 1):
            print(f"\n测试用例 {i}:")
            print(f"标题: {test_case['title']}")
            print(f"步骤: {test_case['test_steps']}")

asyncio.run(generate_tests())
```

### 导出Excel示例

```python
async def export_excel():
    async with Client("http://127.0.0.1:8002/sse") as client:
        # 先生成测试用例
        test_result = await client.call_tool(
            "generate_functional_tests",
            {
                "feature_name": "用户注册",
                "feature_description": "新用户注册功能",
                "acceptance_criteria": ["填写信息注册", "邮箱验证"],
                "priority": "high"
            }
        )
        
        # 导出到Excel
        export_result = await client.call_tool(
            "export_to_excel",
            {
                "test_cases": test_result["test_cases"],
                "filename": "my_test_cases.xlsx",
                "include_summary": True
            }
        )
        
        if export_result["success"]:
            print(f"✅ 导出成功: {export_result['filepath']}")
        else:
            print(f"❌ 导出失败: {export_result['error']}")

asyncio.run(export_excel())
```

## 🛠️ 常用命令

```bash
# 启动服务器
python start.py server

# 运行演示
python start.py demo

# 运行示例
python start.py examples

# 运行测试
python start.py test

# 查看系统状态
python start.py status

# 交互模式
python client.py
```

## 📊 工具功能概览

| 工具名称 | 功能 | 输入 | 输出 |
|---------|------|------|------|
| `analyze_requirements` | 需求分析 | 需求文档 | 测试点、业务规则 |
| `generate_functional_tests` | 功能测试用例 | 功能描述、验收标准 | 测试用例列表 |
| `generate_ui_tests` | UI测试用例 | 页面信息、UI元素 | UI测试用例 |
| `generate_boundary_tests` | 边界值测试 | 输入字段、业务规则 | 边界测试用例 |
| `create_test_scenarios` | 测试场景 | 工作流程、用户角色 | 测试场景 |
| `format_test_case` | 格式化 | 测试用例、格式类型 | 格式化结果 |
| `export_to_excel` | Excel导出 | 测试用例列表 | Excel文件 |
| `validate_test_coverage` | 覆盖率验证 | 测试用例、需求 | 覆盖率报告 |

## 🎯 典型使用场景

### 场景1: 从用户故事生成测试用例

1. **分析用户故事** → `analyze_requirements`
2. **生成功能测试** → `generate_functional_tests`
3. **生成UI测试** → `generate_ui_tests`
4. **导出Excel** → `export_to_excel`

### 场景2: API接口测试用例生成

1. **分析接口文档** → `analyze_requirements`
2. **生成边界值测试** → `generate_boundary_tests`
3. **格式化用例** → `format_test_case`
4. **验证覆盖率** → `validate_test_coverage`

### 场景3: 业务流程测试

1. **创建测试场景** → `create_test_scenarios`
2. **生成功能测试** → `generate_functional_tests`
3. **导出多种格式** → `export_to_excel`, `export_to_json`

## 🔧 配置选项

### 环境变量

```bash
# 服务器配置
export MCP_HOST=127.0.0.1
export MCP_PORT=8002
export MCP_TRANSPORT=sse

# 日志配置
export LOG_LEVEL=INFO
```

### 自定义配置

编辑 `config.py` 文件可以修改：
- 默认优先级和分类
- 支持的UI元素类型
- 导出格式配置
- 测试数据生成规则

## 📁 输出文件

生成的文件保存在以下目录：
- `exports/` - 导出的测试用例文件
- `logs/` - 服务器日志文件
- `templates/` - 测试用例模板

## ❓ 常见问题

### Q: 服务器启动失败？
A: 检查依赖是否安装完整：`pip install -r requirements.txt`

### Q: 连接服务器失败？
A: 确认服务器正在运行，检查端口8002是否被占用

### Q: 导出文件在哪里？
A: 默认保存在项目的 `exports/` 目录下

### Q: 如何自定义测试用例模板？
A: 编辑 `templates/` 目录下的JSON模板文件

### Q: 支持哪些导出格式？
A: 支持Excel (.xlsx)、CSV (.csv)、JSON (.json)、TestLink XML

## 🔗 更多资源

- [完整API文档](API_DOCUMENTATION.md)
- [项目README](README.md)
- [使用示例](examples/basic_usage.py)
- [测试文件](tests/test_basic.py)

## 💡 提示

1. **批量处理**: 可以一次生成多个测试用例
2. **格式转换**: 支持多种测试管理工具格式
3. **覆盖率分析**: 自动检查测试覆盖情况
4. **模板定制**: 可以创建自定义测试用例模板
5. **日志调试**: 查看 `logs/` 目录下的日志文件排查问题

开始你的测试用例生成之旅吧！🎉
